import React, { useEffect } from "react";
import { router } from "expo-router";
import { Box } from "@/components/ui/box";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import { useAuth } from "@/contexts/AuthContext";

export default function SplashScreen() {
  const { user, loading } = useAuth();

  useEffect(() => {
    if (!loading && user) {
      // User is authenticated, redirect to main app
      router.replace("/(tabs)");
    }
  }, [user, loading]);

  // Show loading spinner while checking auth state
  if (loading) {
    return (
      <Box className="flex-1 bg-background-0 justify-center items-center">
        <VStack space="xl" className="items-center">
          <VStack space="md" className="items-center">
            <Heading size="3xl" className="text-typography-900 font-bold">
              Indie Points
            </Heading>
            <Text size="lg" className="text-typography-600 text-center">
              Earn rewards with every purchase
            </Text>
          </VStack>

          <Spinner size="large" />
        </VStack>
      </Box>
    );
  }

  // Show splash screen with auth buttons when not authenticated
  return (
    <Box className="flex-1 bg-background-0 justify-center items-center px-6">
      <VStack space="2xl" className="items-center w-full max-w-md">
        {/* Header Section */}
        <VStack space="lg" className="items-center">
          <Heading
            size="3xl"
            className="text-typography-900 font-bold text-center"
          >
            Welcome to Indie Points
          </Heading>

          {/* Colored divider line */}
          <HStack space="xs" className="w-full max-w-xs">
            <Box className="flex-1 h-1 bg-primary-500 rounded-full" />
            <Box className="flex-1 h-1 bg-secondary-500 rounded-full" />
            <Box className="flex-1 h-1 bg-error-500 rounded-full" />
          </HStack>

          <Text size="lg" className="text-typography-600 text-center">
            Earn points, get rewards, support local businesses
          </Text>
        </VStack>

        {/* Features Section */}
        <VStack space="md" className="w-full">
          {/* Earn Points Feature */}
          <HStack
            space="md"
            className="items-start p-4 bg-background-50 rounded-xl border-2 border-outline-200 shadow-sm"
          >
            <Box className="w-12 h-12 bg-primary-500 rounded-lg items-center justify-center shadow-md"></Box>
            <VStack space="xs" className="flex-1">
              <Heading size="md" className="text-typography-900 font-semibold">
                Earn points
              </Heading>
              <Text size="sm" className="text-typography-600">
                Get points for every purchase at participating businesses
              </Text>
            </VStack>
          </HStack>

          {/* Redeem Rewards Feature */}
          <HStack
            space="md"
            className="items-start p-4 bg-background-50 rounded-xl border-2 border-outline-200 shadow-sm"
          >
            <Box className="w-12 h-12 bg-secondary-500 rounded-lg items-center justify-center shadow-md"></Box>
            <VStack space="xs" className="flex-1">
              <Heading size="md" className="text-typography-900 font-semibold">
                Redeem rewards
              </Heading>
              <Text size="sm" className="text-typography-600">
                Use your points to get discounts and free items
              </Text>
            </VStack>
          </HStack>

          {/* Support Local Feature */}
          <HStack
            space="md"
            className="items-start p-4 bg-background-50 rounded-xl border-2 border-outline-200 shadow-sm"
          >
            <Box className="w-12 h-12 bg-error-500 rounded-lg items-center justify-center shadow-md"></Box>
            <VStack space="xs" className="flex-1">
              <Heading size="md" className="text-typography-900 font-semibold">
                Support local
              </Heading>
              <Text size="sm" className="text-typography-600">
                Help your favorite local businesses grow and thrive
              </Text>
            </VStack>
          </HStack>
        </VStack>

        {/* Action Buttons */}
        <VStack space="md" className="w-full">
          <Button
            size="lg"
            className="w-full bg-primary-500 rounded-xl border-2 border-primary-700 shadow-lg"
            onPress={() => router.push("/(auth)/sign-up")}
          >
            <ButtonText className="text-white font-semibold text-lg">
              Get started
            </ButtonText>
          </Button>

          <HStack space="xs" className="items-center justify-center">
            <Text size="sm" className="text-typography-600">
              Already have an account?
            </Text>
            <Button
              variant="link"
              size="sm"
              onPress={() => router.push("/(auth)/sign-in")}
            >
              <ButtonText className="text-primary-500 font-semibold">
                Sign in
              </ButtonText>
            </Button>
          </HStack>
        </VStack>
      </VStack>
    </Box>
  );
}
