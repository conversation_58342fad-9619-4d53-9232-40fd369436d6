import React, { useEffect } from "react";
import { Link, router } from "expo-router";
import { Box } from "@/components/ui/box";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { Button, ButtonText, ButtonSpinner } from "@/components/ui/button";
import { Input, InputField } from "@/components/ui/input";
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
} from "@/components/ui/form-control";
import { useAuthForm } from "@/hooks/useAuthForm";
import { useAuth } from "@/contexts/AuthContext";
import { SocialLoginButtons } from "@/components/auth/SocialLoginButtons";
import { GradientBar } from "@/components/auth/GradientBar";

export default function SignUp() {
  const { user, signInWithProvider } = useAuth();
  const { formState, errors, loading, updateField, handleSubmit } =
    useAuthForm("signup");

  useEffect(() => {
    if (user) {
      router.replace("/(tabs)");
    }
  }, [user]);

  const onSubmit = async () => {
    await handleSubmit();
    if (!errors.general) {
      // Show success message or redirect to email verification
      router.push("/(auth)/sign-in");
    }
  };

  const handleSocialLogin = async (
    provider: "google" | "facebook" | "twitter"
  ) => {
    try {
      const { error } = await signInWithProvider(provider);
      if (error) {
        console.error(`Error signing in with ${provider}:`, error.message);
      }
    } catch (error) {
      console.error(`Unexpected error signing in with ${provider}:`, error);
    }
  };

  return (
    <Box className="flex-1 bg-background-0 justify-center px-6">
      <VStack space="xl" className="w-full max-w-md mx-auto">
        {/* Header with gradient bar */}
        <VStack space="lg" className="items-center">
          <Heading size="2xl" className="text-typography-900 font-bold">
            Join Indie Points
          </Heading>

          {/* Colored gradient bar */}
          <GradientBar />
        </VStack>

        {/* Email, Password, and Confirm Password Form */}
        <VStack space="lg">
          <FormControl isInvalid={!!errors.email}>
            <FormControlLabel>
              <FormControlLabelText className="text-typography-900 font-medium">
                Email <Text className="text-error-500">*</Text>
              </FormControlLabelText>
            </FormControlLabel>
            <Input className="border-2 border-outline-300 rounded-xl bg-background-0">
              <InputField
                type="text"
                placeholder="Enter your email"
                value={formState.email}
                onChangeText={(text) => updateField("email", text)}
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                className="text-typography-900"
              />
            </Input>
            <FormControlError>
              <FormControlErrorText>{errors.email}</FormControlErrorText>
            </FormControlError>
          </FormControl>

          <FormControl isInvalid={!!errors.password}>
            <FormControlLabel>
              <FormControlLabelText className="text-typography-900 font-medium">
                Password <Text className="text-error-500">*</Text>
              </FormControlLabelText>
            </FormControlLabel>
            <Input className="border-2 border-outline-300 rounded-xl bg-background-0">
              <InputField
                type="password"
                placeholder="Enter your password"
                value={formState.password}
                onChangeText={(text) => updateField("password", text)}
                secureTextEntry
                autoComplete="new-password"
                className="text-typography-900"
              />
            </Input>
            <FormControlError>
              <FormControlErrorText>{errors.password}</FormControlErrorText>
            </FormControlError>
          </FormControl>

          <FormControl isInvalid={!!errors.confirmPassword}>
            <FormControlLabel>
              <FormControlLabelText className="text-typography-900 font-medium">
                Confirm Password <Text className="text-error-500">*</Text>
              </FormControlLabelText>
            </FormControlLabel>
            <Input className="border-2 border-outline-300 rounded-xl bg-background-0">
              <InputField
                type="password"
                placeholder="Confirm your password"
                value={formState.confirmPassword}
                onChangeText={(text) => updateField("confirmPassword", text)}
                secureTextEntry
                autoComplete="new-password"
                className="text-typography-900"
              />
            </Input>
            <FormControlError>
              <FormControlErrorText>
                {errors.confirmPassword}
              </FormControlErrorText>
            </FormControlError>
          </FormControl>

          {errors.general && (
            <Text size="sm" className="text-error-500 text-center">
              {errors.general}
            </Text>
          )}

          {/* Create Account Button */}
          <Button
            onPress={onSubmit}
            isDisabled={loading}
            className="w-full bg-primary-500 rounded-xl border-2 border-primary-700 shadow-lg"
            size="lg"
          >
            {loading && <ButtonSpinner />}
            <ButtonText className="text-white font-semibold text-lg">
              {loading ? "Creating Account..." : "Create Account"}
            </ButtonText>
          </Button>

          {/* Divider */}
          <HStack className="items-center" space="md">
            <Box className="flex-1 h-px bg-outline-200" />
            <Text size="sm" className="text-typography-500">
              or continue with
            </Text>
            <Box className="flex-1 h-px bg-outline-200" />
          </HStack>

          {/* Social Login Buttons */}
          <SocialLoginButtons onSocialLogin={handleSocialLogin} />
        </VStack>

        {/* Sign In Link */}
        <HStack className="justify-center items-center" space="xs">
          <Text size="sm" className="text-typography-500">
            Already have an account?
          </Text>
          <Link href="/(auth)/sign-in" asChild>
            <Text
              size="sm"
              className="text-primary-600 font-semibold underline"
            >
              Sign In
            </Text>
          </Link>
        </HStack>

        {/* Back to Welcome Link */}
        <HStack className="justify-center">
          <Link href="/" asChild>
            <Text size="sm" className="text-primary-600 underline">
              ← Back to Welcome
            </Text>
          </Link>
        </HStack>
      </VStack>
    </Box>
  );
}
