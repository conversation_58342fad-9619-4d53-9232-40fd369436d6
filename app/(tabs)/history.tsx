import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>iew } from "react-native";
import { Box } from "@/components/ui/box";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import FontAwesome from "@expo/vector-icons/FontAwesome";

const transactionData = [
  {
    id: 1,
    type: "purchase",
    business: "StuMac Solutions",
    category: "Gym/Fitness",
    points: 463,
    amount: "£463.00 spent",
    date: "11 Jun 2025, 16:37",
  },
  {
    id: 2,
    type: "redeem",
    business: "StuMac Solutions",
    category: "Gym/Fitness",
    points: -740,
    amount: "",
    date: "11 Jun 2025, 16:37",
  },
  {
    id: 3,
    type: "purchase",
    business: "StuMac Solutions",
    category: "Gym/Fitness",
    points: 20,
    amount: "£19.99 spent",
    date: "11 Jun 2025, 16:36",
  },
  {
    id: 4,
    type: "redeem",
    business: "StuMac Solutions",
    category: "Gym/Fitness",
    points: -20,
    amount: "",
    date: "11 Jun 2025, 16:35",
  },
  {
    id: 5,
    type: "purchase",
    business: "StuMac Solutions",
    category: "Gym/Fitness",
    points: 999,
    amount: "£999.00 spent",
    date: "11 Jun 2025, 16:35",
  },
  {
    id: 6,
    type: "purchase",
    business: "StuMac Solutions",
    category: "Gym/Fitness",
    points: 24,
    amount: "£23.99 spent",
    date: "11 Jun 2025, 16:34",
  },
];

const businessData = [
  {
    id: 1,
    name: "StuMac Solutions",
    category: "Gym/Fitness",
    points: 759,
    lastVisit: "11 Jun 2025",
  },
  {
    id: 2,
    name: "Indie Points",
    category: "System",
    points: 1,
    lastVisit: "11 Jun 2025",
  },
];

export default function History() {
  const [activeTab, setActiveTab] = useState<"transactions" | "businesses">(
    "transactions"
  );

  return (
    <Box className="flex-1 bg-background-0">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space="lg" className="px-6 pt-6 pb-6">
          {/* Title */}
          <Heading size="3xl" className="text-typography-900 font-bold">
            History
          </Heading>

          {/* Colored divider line */}
          <HStack space="xs" className="w-full">
            <Box className="flex-1 h-1 bg-primary-500 rounded-full" />
            <Box className="flex-1 h-1 bg-secondary-500 rounded-full" />
            <Box className="flex-1 h-1 bg-error-500 rounded-full" />
          </HStack>

          {/* Tab Toggle */}
          <HStack className="bg-white rounded-2xl border-4 border-typography-900 shadow-lg overflow-hidden">
            <Pressable
              className={`flex-1 py-3 px-6 ${
                activeTab === "transactions" ? "bg-primary-500" : "bg-white"
              }`}
              onPress={() => setActiveTab("transactions")}
            >
              <Text
                size="md"
                className={`font-semibold text-center ${
                  activeTab === "transactions"
                    ? "text-white"
                    : "text-typography-900"
                }`}
              >
                Transactions
              </Text>
            </Pressable>

            <Pressable
              className={`flex-1 py-3 px-6 ${
                activeTab === "businesses" ? "bg-primary-500" : "bg-white"
              }`}
              onPress={() => setActiveTab("businesses")}
            >
              <Text
                size="md"
                className={`font-semibold text-center ${
                  activeTab === "businesses"
                    ? "text-white"
                    : "text-typography-900"
                }`}
              >
                Businesses
              </Text>
            </Pressable>
          </HStack>
        </VStack>

        <Box className="px-6 pb-8">
          {activeTab === "transactions" ? (
            <VStack space="md">
              {transactionData.map((item) => (
                <Box
                  key={item.id}
                  className="bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-4"
                >
                  <HStack space="md" className="items-center">
                    {/* Icon */}
                    <Box
                      className={`w-12 h-12 rounded-xl items-center justify-center ${
                        item.type === "purchase"
                          ? "bg-primary-500"
                          : "bg-error-500"
                      }`}
                    >
                      <FontAwesome
                        name={
                          item.type === "purchase" ? "shopping-bag" : "gift"
                        }
                        size={20}
                        color="white"
                      />
                    </Box>

                    {/* Content */}
                    <VStack className="flex-1">
                      <Text
                        size="md"
                        className="text-typography-900 font-semibold"
                      >
                        {item.business}
                      </Text>
                      <Text size="sm" className="text-typography-600">
                        {item.type === "purchase" ? "Purchase" : "Redemption"} •{" "}
                        {item.category}
                      </Text>
                      <Text size="xs" className="text-typography-500">
                        {item.date}
                      </Text>
                      {item.amount && (
                        <Text size="xs" className="text-typography-500">
                          {item.amount}
                        </Text>
                      )}
                    </VStack>

                    {/* Points */}
                    <Text
                      size="lg"
                      className={`font-bold ${
                        item.points > 0 ? "text-primary-500" : "text-error-500"
                      }`}
                    >
                      {item.points > 0 ? "+" : ""}
                      {item.points} pts
                    </Text>
                  </HStack>
                </Box>
              ))}
            </VStack>
          ) : (
            <VStack space="md">
              {businessData.map((business) => (
                <Box
                  key={business.id}
                  className="bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-4"
                >
                  <HStack space="md" className="items-center">
                    {/* Icon */}
                    <Box className="w-12 h-12 bg-primary-500 rounded-xl items-center justify-center">
                      <FontAwesome name="building" size={20} color="white" />
                    </Box>

                    {/* Content */}
                    <VStack className="flex-1">
                      <Text
                        size="md"
                        className="text-typography-900 font-semibold"
                      >
                        {business.name}
                      </Text>
                      <Text size="sm" className="text-typography-600">
                        {business.category}
                      </Text>
                      <Text size="xs" className="text-typography-500">
                        Last visit: {business.lastVisit}
                      </Text>
                    </VStack>

                    {/* Points */}
                    <Text size="lg" className="text-primary-500 font-bold">
                      {business.points} pts
                    </Text>
                  </HStack>
                </Box>
              ))}
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
