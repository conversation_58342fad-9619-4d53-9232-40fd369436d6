import React from "react";
import { ScrollView } from "react-native";
import { Box } from "@/components/ui/box";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { useAuth } from "@/contexts/AuthContext";

export default function Home() {
  const { user } = useAuth();

  // Extract name from email (everything before @)
  const getUserName = () => {
    if (!user?.email) return "User";
    const emailParts = user.email.split("@");
    const namePart = emailParts[0];
    // Convert to proper case (first letter uppercase, rest lowercase)
    return namePart.charAt(0).toUpperCase() + namePart.slice(1).toLowerCase();
  };

  return (
    <Box className="flex-1 bg-background-0">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space="lg" className="px-6 pt-6 pb-6">
          {/* Title */}
          <Heading size="3xl" className="text-typography-900 font-bold">
            Home
          </Heading>

          {/* Colored divider line */}
          <HStack space="xs" className="w-full">
            <Box className="flex-1 h-1 bg-primary-500 rounded-full" />
            <Box className="flex-1 h-1 bg-secondary-500 rounded-full" />
            <Box className="flex-1 h-1 bg-error-500 rounded-full" />
          </HStack>

          {/* Greeting */}
          <VStack space="xs" className="items-center">
            <Heading
              size="xl"
              className="text-typography-900 font-semibold text-center"
            >
              Good afternoon, {getUserName()}
            </Heading>
            <Heading
              size="xl"
              className="text-typography-900 font-semibold text-center"
            >
              Indie Points!
            </Heading>
            <Text size="md" className="text-typography-600 text-center">
              Here&apos;s your points summary.
            </Text>
          </VStack>
        </VStack>

        <Box className="px-6 pb-8">
          <VStack space="lg">
            {/* Active Points Card */}
            <Box className="bg-primary-500 rounded-2xl border-4 border-primary-700 shadow-lg p-6">
              <VStack space="xs">
                <Text size="lg" className="text-white font-medium">
                  Active points
                </Text>
                <Heading size="4xl" className="text-white font-bold">
                  760
                </Heading>
              </VStack>
            </Box>

            {/* Total Earned and Redeemed Cards */}
            <HStack space="md">
              {/* Total Earned Card */}
              <Box className="flex-1 bg-secondary-500 rounded-2xl border-4 border-secondary-700 shadow-lg p-4">
                <VStack space="xs">
                  <Text size="md" className="text-white font-medium">
                    Total earned
                  </Text>
                  <Heading size="2xl" className="text-white font-bold">
                    1780
                  </Heading>
                </VStack>
              </Box>

              {/* Total Redeemed Card */}
              <Box className="flex-1 bg-error-500 rounded-2xl border-4 border-error-700 shadow-lg p-4">
                <VStack space="xs">
                  <Text size="md" className="text-white font-medium">
                    Total redeemed
                  </Text>
                  <Heading size="2xl" className="text-white font-bold">
                    1020
                  </Heading>
                </VStack>
              </Box>
            </HStack>
          </VStack>
        </Box>
      </ScrollView>
    </Box>
  );
}
